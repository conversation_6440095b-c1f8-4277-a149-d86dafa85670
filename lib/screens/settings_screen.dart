import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../providers/localization_provider.dart';
import '../l10n/app_localizations_extension.dart';
import '../services/broadcast_service.dart';
import '../services/equipment_api_service.dart';
import '../services/orientation_service.dart';
import '../utils/ui_utils.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final _mqttServerController = TextEditingController();
  final _apiServerController = TextEditingController();
  final _mqttPortController = TextEditingController();
  final _groupNameController = TextEditingController();
  final _deviceAliasController = TextEditingController();
  final _registrationCodeController = TextEditingController();
  final BroadcastService _broadcastService = BroadcastService();
  late final SettingsProvider _settingsProvider;

  String _selectedLanguage = 'zh';
  String _selectedOrientation = 'landscape';
  bool _isDiscovering = false;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    UiUtils.setFullscreenMode();

    _settingsProvider = Provider.of<SettingsProvider>(context, listen: false);
    _loadSettings();

    _settingsProvider.addListener(_onSettingsChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_mqttServerController.text.isEmpty) {
        _discoverServer();
      }
    });
  }

  void _loadSettings() {
    final settings = _settingsProvider.settings;
    _mqttServerController.text = settings.mqttServerAddress ?? '';
    _apiServerController.text = settings.serverPort ?? '8090';
    _mqttPortController.text = settings.mqttPort ?? '1883';
    _groupNameController.text = settings.groupName ?? '';
    _deviceAliasController.text = settings.deviceAlias ?? '';
    _registrationCodeController.text = settings.registrationCode ?? '';

    setState(() {
      _selectedLanguage = settings.languageCode ?? 'zh';
      _selectedOrientation = settings.screenOrientation ?? 'landscape';
    });
  }

  void _onSettingsChanged() {
    if (_isSaving) return;

    final settings = _settingsProvider.settings;
    bool changed = false;

    if (_registrationCodeController.text != (settings.registrationCode ?? '')) {
      _registrationCodeController.text = settings.registrationCode ?? '';
      changed = true;
    }
    if (_groupNameController.text != (settings.groupName ?? '')) {
      _groupNameController.text = settings.groupName ?? '';
      changed = true;
    }
    if (_deviceAliasController.text != (settings.deviceAlias ?? '')) {
      _deviceAliasController.text = settings.deviceAlias ?? '';
      changed = true;
    }

    if (changed && mounted) {
      setState(() {});
    }
  }

  @override
  void dispose() {
    _settingsProvider.removeListener(_onSettingsChanged);
    _mqttServerController.dispose();
    _apiServerController.dispose();
    _mqttPortController.dispose();
    _groupNameController.dispose();
    _deviceAliasController.dispose();
    _registrationCodeController.dispose();
    _broadcastService.close();
    super.dispose();
  }

  Future<void> _discoverServer() async {
    if (_isDiscovering) return;

    setState(() {
      _isDiscovering = true;
    });

    final discoveryResult = await _broadcastService.listenForBroadcast(
      timeout: const Duration(seconds: 10),
    );

    if (mounted) {
      setState(() {
        _isDiscovering = false;
      });

      if (discoveryResult != null) {
        final serverAddress = discoveryResult['serverAddress'];
        final salt = discoveryResult['salt'];

        if (serverAddress != null) {
          setState(() {
            _mqttServerController.text = serverAddress;
          });

          if (salt != null) {
            await _settingsProvider.generateAndSaveRegistrationCode(salt);
          }
          _submit(isAutoDiscovered: true);
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(context.l10n.discoveryFailed)));
        }
      }
    }
  }

  Future<bool> _saveSettings() async {
    final localizationProvider = Provider.of<LocalizationProvider>(
      context,
      listen: false,
    );

    await _settingsProvider.updateMqttServerAddress(_mqttServerController.text);
    await _settingsProvider.updateServerPort(_apiServerController.text);
    await _settingsProvider.updateMqttPort(_mqttPortController.text);
    await _settingsProvider.updateGroupName(_groupNameController.text);
    await _settingsProvider.updateDeviceAlias(_deviceAliasController.text);
    await _settingsProvider.updateRegistrationCode(
      _registrationCodeController.text.toUpperCase(),
    );

    if (_selectedLanguage != _settingsProvider.settings.languageCode) {
      await _settingsProvider.updateLanguageCode(_selectedLanguage);
      await localizationProvider.changeLocale(Locale(_selectedLanguage));
    }

    if (_selectedOrientation != _settingsProvider.settings.screenOrientation) {
      await _settingsProvider.updateScreenOrientation(_selectedOrientation);
      await OrientationService.applyOrientation(_selectedOrientation);
    }
    return true;
  }

  Future<void> _submit({bool isAutoDiscovered = false}) async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    final apiService = EquipmentApiService();
    final settings = _settingsProvider.settings;

    final response = await apiService.addEquipment(
      serverAddress: _mqttServerController.text,
      serverPort: _apiServerController.text,
      deviceAlias: _deviceAliasController.text,
      macAddress: settings.macAddress ?? '',
      groupName: _groupNameController.text,
      aliasName: _deviceAliasController.text,
      registrationCode: _registrationCodeController.text.toUpperCase(),
    );

    if (!mounted) return;

    if (response['code'] == 0) {
      final saved = await _saveSettings();
      if (saved && mounted) {
        if (!isAutoDiscovered) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text(context.l10n.settingsSaved)));
        }
        Navigator.of(context).pop();
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '${context.l10n.registrationFailed}: ${response['message']}',
          ),
        ),
      );
    }

    if (mounted) {
      setState(() {
        _isSaving = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.settings),
        toolbarHeight: 48, // 减少AppBar高度
      ),
      body: Consumer<SettingsProvider>(
        builder: (context, settingsProvider, child) {
          return Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 16, 8), // 减少左右边距
            child: Form(
              key: _formKey,
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 将语言和方向选择合并到一行
                    _buildCompactSelections(),
                    const SizedBox(height: 8),

                    // 服务器配置区域
                    _buildSectionTitle('服务器配置'),
                    const SizedBox(height: 4),
                    _buildCompactTextFormField(
                      controller: _mqttServerController,
                      labelText: context.l10n.mqttServerAddress,
                      hintText: _isDiscovering
                          ? context.l10n.discovering
                          : context.l10n.mqttServerAddressHint,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterMqttServerAddress;
                        }
                        return null;
                      },
                      suffixIcon: IconButton(
                        icon: _isDiscovering
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : const Icon(Icons.search, size: 20),
                        onPressed: _discoverServer,
                      ),
                    ),

                    // 端口配置使用两列布局
                    Row(
                      children: [
                        Expanded(
                          child: _buildCompactTextFormField(
                            controller: _apiServerController,
                            labelText: context.l10n.apiServerPort,
                            hintText: context.l10n.apiServerPortHint,
                            keyboardType: TextInputType.number,
                            validator: _validatePort,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: _buildCompactTextFormField(
                            controller: _mqttPortController,
                            labelText: context.l10n.mqttPort,
                            hintText: context.l10n.mqttPortHint,
                            keyboardType: TextInputType.number,
                            validator: _validatePort,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),
                    _buildSectionTitle('设备配置'),
                    const SizedBox(height: 4),

                    _buildCompactTextFormField(
                      controller: _groupNameController,
                      labelText: context.l10n.groupName,
                      hintText: context.l10n.groupNameHint,
                    ),
                    _buildCompactTextFormField(
                      controller: _deviceAliasController,
                      labelText: context.l10n.deviceAlias,
                      hintText: context.l10n.deviceAliasHint,
                    ),
                    _buildCompactTextFormField(
                      controller: _registrationCodeController,
                      labelText: context.l10n.registrationCode,
                      hintText: context.l10n.registrationCodeHint,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return context.l10n.pleaseEnterRegistrationCode;
                        }
                        return null;
                      },
                    ),

                    // MAC地址显示
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 4.0),
                      child: TextFormField(
                        initialValue:
                            settingsProvider.settings.macAddress ??
                            context.l10n.loading,
                        decoration: InputDecoration(
                          labelText: context.l10n.macAddress,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          isDense: true,
                        ),
                        readOnly: true,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),

                    const SizedBox(height: 12),
                    // 保存按钮
                    SizedBox(
                      width: double.infinity,
                      height: 44,
                      child: ElevatedButton(
                        onPressed: settingsProvider.isLoading ? null : _submit,
                        style: ElevatedButton.styleFrom(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: settingsProvider.isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              )
                            : Text(
                                context.l10n.saveSettings,
                                style: const TextStyle(fontSize: 16),
                              ),
                      ),
                    ),

                    if (settingsProvider.error.isNotEmpty)
                      Padding(
                        padding: const EdgeInsets.only(top: 8),
                        child: Text(
                          settingsProvider.error,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    const SizedBox(height: 8), // 底部留白
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // 紧凑的选择区域，将语言和方向选择合并
  Widget _buildCompactSelections() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 语言选择
          Text(
            context.l10n.language,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () => setState(() => _selectedLanguage = 'zh'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedLanguage == 'zh'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedLanguage == 'zh'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedLanguage == 'zh'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedLanguage == 'zh'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.chinese,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: () => setState(() => _selectedLanguage = 'en'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedLanguage == 'en'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedLanguage == 'en'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedLanguage == 'en'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedLanguage == 'en'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.english,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // 屏幕方向选择
          Text(
            context.l10n.screenOrientation,
            style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 4),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () =>
                      setState(() => _selectedOrientation = 'landscape'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedOrientation == 'landscape'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedOrientation == 'landscape'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedOrientation == 'landscape'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedOrientation == 'landscape'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.landscapeDisplay,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: InkWell(
                  onTap: () =>
                      setState(() => _selectedOrientation = 'portrait'),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 12,
                    ),
                    decoration: BoxDecoration(
                      color: _selectedOrientation == 'portrait'
                          ? Colors.blue.shade50
                          : null,
                      border: Border.all(
                        color: _selectedOrientation == 'portrait'
                            ? Colors.blue
                            : Colors.grey.shade300,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _selectedOrientation == 'portrait'
                              ? Icons.radio_button_checked
                              : Icons.radio_button_unchecked,
                          size: 16,
                          color: _selectedOrientation == 'portrait'
                              ? Colors.blue
                              : Colors.grey,
                        ),
                        const SizedBox(width: 6),
                        Text(
                          context.l10n.portraitDisplay,
                          style: const TextStyle(fontSize: 13),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 区域标题
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 15,
          fontWeight: FontWeight.w600,
          color: Colors.black87,
        ),
      ),
    );
  }

  // 紧凑的文本输入框
  Widget _buildCompactTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    Widget? suffixIcon,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: TextFormField(
        controller: controller,
        keyboardType: keyboardType,
        decoration: InputDecoration(
          labelText: labelText,
          hintText: hintText,
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(8.0)),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 8,
          ),
          isDense: true,
          suffixIcon: suffixIcon,
        ),
        validator: validator,
        style: const TextStyle(fontSize: 14),
      ),
    );
  }

  String? _validatePort(String? value) {
    if (value == null || value.isEmpty) {
      return context.l10n.pleaseEnterMqttPort;
    }
    try {
      final port = int.parse(value);
      if (port <= 0 || port > 65535) {
        return context.l10n.portNumberMustBeBetween;
      }
    } catch (e) {
      return context.l10n.pleaseEnterValidPort;
    }
    return null;
  }
}
