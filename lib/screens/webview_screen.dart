import 'dart:io';
import 'package:esop_client/screens/settings_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:provider/provider.dart';
import '../services/webview_manager.dart';
import '../providers/settings_provider.dart';
import '../services/orientation_service.dart';
import '../services/report_service.dart';

class WebViewScreen extends StatefulWidget {
  final File htmlFile;

  const WebViewScreen({super.key, required this.htmlFile});

  @override
  State<WebViewScreen> createState() => _WebViewScreenState();
}

class _WebViewScreenState extends State<WebViewScreen> {
  InAppWebViewController? _webViewController;
  bool _isLoading = true;
  String _error = '';
  DateTime? _loadStartTime;

  final WebViewManager _webViewManager = WebViewManager();
  final ReportService _reportService = ReportService();

  @override
  void initState() {
    super.initState();

    // Ensure fullscreen mode is maintained
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.immersiveSticky,
      overlays: [],
    );

    // Close any existing WebView before creating new one
    _webViewManager.closeCurrentWebView();

    // Apply orientation setting
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _applyOrientationSetting();
    });
  }

  // Apply orientation setting from settings
  Future<void> _applyOrientationSetting() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final orientation =
        settingsProvider.settings.screenOrientation ?? 'landscape';
    await OrientationService.applyOrientation(orientation);
  }

  // Handle back button press
  Future<bool> _handleBackPressed() async {
    if (_webViewController != null) {
      if (await _webViewController!.canGoBack()) {
        // If WebView can go back, go back in WebView
        _webViewController!.goBack();
        return false; // Don't exit the app
      }
    }
    return true; // Allow app to handle the back button (exit WebView)
  }

  @override
  void dispose() {
    // Clear current WebView controller
    _webViewManager.currentWebViewController = null;

    // Restore system UI overlays
    SystemChrome.setEnabledSystemUIMode(
      SystemUiMode.edgeToEdge,
      overlays: SystemUiOverlay.values,
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvoked: (didPop) async {
        if (didPop) return;
        final bool canPop = await _handleBackPressed();
        if (canPop && context.mounted) {
          Navigator.of(context).pop();
        }
      },
      child: Scaffold(
        // No AppBar for fullscreen experience
        body: Stack(
          children: [
            // WebView
            InAppWebView(
              initialUrlRequest: URLRequest(
                url: WebUri.uri(Uri.file(widget.htmlFile.path)),
              ),
              initialSettings: InAppWebViewSettings(
                useShouldOverrideUrlLoading: true,
                mediaPlaybackRequiresUserGesture: false,
                javaScriptEnabled: true,
                supportZoom: false,
                useOnLoadResource: true,
                transparentBackground: true,
                // Android specific settings
                useHybridComposition: true,
                // iOS specific settings
                allowsInlineMediaPlayback: true,
              ),
              onWebViewCreated: (controller) {
                _webViewController = controller;
                _webViewManager.currentWebViewController = controller;
              },
              onLoadStart: (controller, url) {
                _loadStartTime = DateTime.now();
                setState(() {
                  _isLoading = true;
                  _error = '';
                });
              },
              onLoadStop: (controller, url) {
                setState(() {
                  _isLoading = false;
                });

                // 上报WebView加载完成
                if (_loadStartTime != null) {
                  final loadDuration =
                      DateTime.now()
                          .difference(_loadStartTime!)
                          .inMilliseconds /
                      1000.0;
                  _reportService.reportWebviewLoaded(
                    url: url?.toString() ?? '',
                    htmlFilePath: widget.htmlFile.path,
                    loadDuration: loadDuration,
                  );
                }
              },
              onReceivedError: (controller, request, error) {
                setState(() {
                  _isLoading = false;
                  _error = 'Error loading content: ${error.description}';
                });

                // 上报WebView加载错误
                _reportService.reportWebviewError(
                  url: request.url.toString(),
                  htmlFilePath: widget.htmlFile.path,
                  errorMessage: 'Error loading content: ${error.description}',
                );
              },
              onReceivedHttpError: (controller, request, errorResponse) {
                setState(() {
                  _isLoading = false;
                  _error =
                      'HTTP Error: ${errorResponse.statusCode} ${errorResponse.reasonPhrase}';
                });

                // 上报WebView HTTP错误
                _reportService.reportWebviewError(
                  url: request.url.toString(),
                  htmlFilePath: widget.htmlFile.path,
                  errorMessage:
                      'HTTP Error: ${errorResponse.statusCode} ${errorResponse.reasonPhrase}',
                );
              },
            ),

            // Loading indicator
            if (_isLoading) const Center(child: CircularProgressIndicator()),

            // Error message
            // if (_error.isNotEmpty)
            //   Center(
            //     child: Padding(
            //       padding: const EdgeInsets.all(16.0),
            //       child: Column(
            //         mainAxisSize: MainAxisSize.min,
            //         children: [
            //           Text(
            //             _error,
            //             style: const TextStyle(color: Colors.red),
            //             textAlign: TextAlign.center,
            //           ),
            //           const SizedBox(height: 16),
            //           ElevatedButton(
            //             onPressed: () {
            //               _webViewController?.reload();
            //             },
            //             child: const Text('Retry'),
            //           ),
            //         ],
            //       ),
            //     ),
            //   ),

            // Floating action buttons
            Positioned(
              bottom: 10,
              right: 10,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(height: 10),
                  // // Refresh button
                  FloatingActionButton(
                    heroTag: "settings",
                    mini: true,
                    backgroundColor: Colors.black.withAlpha(70),
                    child: const Icon(
                      Icons.settings,
                      color: Color.fromARGB(200, 255, 255, 255),
                    ),
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SettingsScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
