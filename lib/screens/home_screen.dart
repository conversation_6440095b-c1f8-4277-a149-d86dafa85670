import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../providers/mqtt_provider.dart';
import '../providers/file_provider.dart';
import '../providers/settings_provider.dart';
import '../models/mqtt_message_model.dart';
import '../services/equipment_api_service.dart';
import '../services/life_signal_service.dart';
import '../services/orientation_service.dart';
import 'settings_screen.dart';
import 'webview_screen.dart';
import 'pdf_viewer_screen.dart';
import 'office_viewer_screen.dart';
import '../utils/gesture_detector.dart';
import '../utils/file_utils.dart';
import '../l10n/app_localizations_extension.dart';
import '../utils/ui_utils.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final EquipmentApiService _apiService = EquipmentApiService();
  final LifeSignalService _lifeSignalService = LifeSignalService();
  bool _ignoreInitialMessages = true;

  // 自动恢复相关
  Timer? _autoRecoveryTimer;
  DateTime? _lastUserInteraction;

  // MQTT重连检查相关
  Timer? _mqttReconnectCheckTimer;

  @override
  void initState() {
    super.initState();
    // initAutoStart();
    // Ensure fullscreen mode is maintained
    UiUtils.setFullscreenMode();

    // Initialize app with required checks
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkRequiredSettings();
      _applyOrientationSetting();
      _startAutoRecoveryCheck();
      _startMqttReconnectCheck();
    });
  }

  // //initializing the autoStart with the first build.
  // Future<void> initAutoStart() async {
  //   //check auto-start availability.
  //   var test = await FlutterAutostartAndroid.isAutoStartPermissionAvailable(
  //     false,
  //   );

  //   print("是否加入自动启动：$test");
  //   //if available then navigate to auto-start setting page.
  //   if (!test) {
  //     final result = await FlutterAutostartAndroid.requestAutoStartPermission();
  //     print('加入自动启动: $result');
  //   }
  // }

  // Apply orientation setting from settings
  Future<void> _applyOrientationSetting() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final orientation =
        settingsProvider.settings.screenOrientation ?? 'landscape';
    await OrientationService.applyOrientation(orientation);
  }

  // Register device with the server
  Future<void> _registerDeviceWithServer() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final settings = settingsProvider.settings;

    // Get required parameters
    final serverAddress = settings.mqttServerAddress ?? '';
    final serverPort = settings.serverPort ?? '';
    final deviceAlias = settings.deviceAlias ?? '';
    final macAddress = settings.macAddress ?? '';
    final groupName = settings.groupName ?? '';
    final registrationCode = settings.registrationCode ?? '';
    final aliasName = settings.deviceAlias ?? '';

    // Skip if server address is empty
    if (serverAddress.isEmpty) {
      debugPrint('Server address is empty, skipping device registration');
      return;
    }

    // Skip if any required parameter is missing
    if (macAddress.isEmpty) {
      debugPrint('Required parameters missing, skipping device registration');
      return;
    }

    // Register device with the server
    try {
      final response = await _apiService.addEquipment(
        serverAddress: serverAddress,
        serverPort: serverPort,
        deviceAlias: deviceAlias,
        macAddress: macAddress,
        groupName: groupName,
        aliasName: aliasName,
        registrationCode: registrationCode,
      );

      if (response['code'] == 0) {
        debugPrint('Device registered successfully with the server');

        // Start life signal after successful registration
        _lifeSignalService.startLifeSignal(settingsProvider: settingsProvider);
      } else {
        debugPrint(
          'Failed to register device with the server: ${response['message']}',
        );
      }
    } catch (e) {
      debugPrint('Error registering device with the server: $e');
    }
  }

  @override
  void dispose() {
    _autoRecoveryTimer?.cancel();
    _mqttReconnectCheckTimer?.cancel();
    // Stop life signal when screen is disposed
    _lifeSignalService.stopLifeSignal();
    super.dispose();
  }

  // 启动自动恢复检查
  void _startAutoRecoveryCheck() {
    // 延迟5秒后开始检查，确保所有初始化完成
    Timer(const Duration(seconds: 5), () {
      _checkAutoRecoveryConditions();
    });
  }

  // 检查自动恢复条件
  Future<void> _checkAutoRecoveryConditions() async {
    if (!mounted) return;

    try {
      final settingsProvider = Provider.of<SettingsProvider>(
        context,
        listen: false,
      );
      final mqttProvider = Provider.of<MqttProvider>(context, listen: false);
      final fileProvider = Provider.of<FileProvider>(context, listen: false);

      // 检查所有必要条件
      final isRegistrationValid = await _isRegistrationCodeValid(
        settingsProvider,
      );
      final isMqttConnected = mqttProvider.isConnected;
      final isNetworkConnected = await _checkNetworkConnectivity();
      final hasExistingFile = await _hasDownloadedFiles(fileProvider);

      debugPrint('Auto recovery check:');
      debugPrint('  Registration valid: $isRegistrationValid');
      debugPrint('  MQTT connected: $isMqttConnected');
      debugPrint('  Network connected: $isNetworkConnected');
      debugPrint('  Has existing file: $hasExistingFile');

      // 如果所有条件都满足，启动10秒倒计时
      if (isRegistrationValid &&
          isMqttConnected &&
          isNetworkConnected &&
          hasExistingFile) {
        _startAutoRecoveryCountdown(fileProvider);
      } else {
        // 如果条件不满足，5秒后重新检查
        Timer(const Duration(seconds: 5), () {
          _checkAutoRecoveryConditions();
        });
      }
    } catch (e) {
      debugPrint('Error checking auto recovery conditions: $e');
      // 出错时5秒后重新检查
      Timer(const Duration(seconds: 5), () {
        _checkAutoRecoveryConditions();
      });
    }
  }

  // 启动自动恢复倒计时
  void _startAutoRecoveryCountdown(FileProvider fileProvider) {
    debugPrint('Starting auto recovery countdown (10 seconds)...');

    // 记录开始倒计时的时间
    _lastUserInteraction = DateTime.now();

    _autoRecoveryTimer?.cancel();
    _autoRecoveryTimer = Timer(const Duration(seconds: 10), () async {
      if (!mounted) return;

      // 检查在倒计时期间是否有用户交互
      final now = DateTime.now();
      final timeSinceLastInteraction = now
          .difference(_lastUserInteraction!)
          .inSeconds;

      if (timeSinceLastInteraction >= 10) {
        debugPrint('Auto recovery triggered - opening existing file');
        await _performAutoRecovery(fileProvider);
      } else {
        debugPrint('User interaction detected, canceling auto recovery');
      }
    });
  }

  // 记录用户交互
  void _recordUserInteraction() {
    _lastUserInteraction = DateTime.now();
    debugPrint('User interaction recorded');
  }

  // 执行自动恢复
  Future<void> _performAutoRecovery(FileProvider fileProvider) async {
    try {
      // 查找并使用现有文件
      final hasExistingFile = await fileProvider.findAndUseExistingFile();

      if (hasExistingFile && mounted) {
        if (fileProvider.indexHtmlFile != null) {
          // 打开HTML文件
          debugPrint('Auto recovery: Opening HTML file');
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  WebViewScreen(htmlFile: fileProvider.indexHtmlFile!),
            ),
          );
        } else if (fileProvider.downloadedFile != null) {
          // 打开文档文件
          final filePath = fileProvider.downloadedFile!.path;
          if (FileUtils.isPdfFile(filePath)) {
            debugPrint('Auto recovery: Opening PDF file');
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) =>
                    PdfViewerScreen(pdfFile: fileProvider.downloadedFile!),
              ),
            );
          } else if (FileUtils.isOfficeFile(filePath)) {
            debugPrint('Auto recovery: Opening Office file');
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => OfficeViewerScreen(filePath: filePath),
              ),
            );
          }
        }
      }
    } catch (e) {
      debugPrint('Error performing auto recovery: $e');
    }
  }

  // 检查注册码是否有效
  Future<bool> _isRegistrationCodeValid(
    SettingsProvider settingsProvider,
  ) async {
    final settings = settingsProvider.settings;
    final registrationCode = settings.registrationCode;
    final serverAddress = settings.mqttServerAddress;

    // 基本检查：注册码和服务器地址不能为空
    if (registrationCode == null || registrationCode.isEmpty) {
      return false;
    }

    if (serverAddress == null || serverAddress.isEmpty) {
      return false;
    }

    // 这里可以添加更复杂的注册码验证逻辑
    // 比如向服务器验证注册码的有效性
    return true;
  }

  // 检查网络连通性
  Future<bool> _checkNetworkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return !connectivityResult.contains(ConnectivityResult.none);
    } catch (e) {
      debugPrint('Error checking network connectivity: $e');
      return false;
    }
  }

  // 检查是否有已下发的文件
  Future<bool> _hasDownloadedFiles(FileProvider fileProvider) async {
    try {
      // 使用FileProvider的findAndUseExistingFile方法来检查
      final hasExistingFile = await fileProvider.findAndUseExistingFile();
      return hasExistingFile;
    } catch (e) {
      debugPrint('Error checking for downloaded files: $e');
      return false;
    }
  }

  // 启动MQTT重连检查
  void _startMqttReconnectCheck() {
    debugPrint('Starting MQTT reconnect check timer...');

    _mqttReconnectCheckTimer?.cancel();
    _mqttReconnectCheckTimer = Timer.periodic(const Duration(seconds: 5), (
      timer,
    ) {
      if (!mounted) return;

      try {
        final mqttProvider = Provider.of<MqttProvider>(context, listen: false);

        // 检查MQTT连接状态
        if (!mqttProvider.isConnected) {
          debugPrint('MQTT disconnected detected, triggering reconnect...');

          // 触发重连
          mqttProvider.mqttService.forceReconnect();
        } else {
          debugPrint('MQTT connection check: connected');
        }
      } catch (e) {
        debugPrint('Error checking MQTT connection: $e');
      }
    });
  }

  // Check if required settings are filled
  Future<void> _checkRequiredSettings() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final mqttProvider = Provider.of<MqttProvider>(context, listen: false);
    final fileProvider = Provider.of<FileProvider>(context, listen: false);

    // Wait for settings to be loaded if they're still loading
    if (settingsProvider.isLoading) {
      debugPrint('Settings are still loading, waiting...');
      // Wait a bit and check again
      await Future.delayed(const Duration(milliseconds: 500));
      if (mounted) {
        _checkRequiredSettings();
      }
      return;
    }

    // Make sure settings are initialized
    if (settingsProvider.settings.macAddress == null ||
        settingsProvider.settings.macAddress!.isEmpty) {
      debugPrint('Settings not fully initialized, initializing...');
      await settingsProvider.initSettings();
    }

    // Check if server address, group name, device alias, and registration code are filled
    final settings = settingsProvider.settings;
    final serverAddress = settings.mqttServerAddress;
    final groupName = settings.groupName;
    final deviceAlias = settings.deviceAlias;
    final registrationCode = settings.registrationCode;

    bool settingsComplete = true;

    // Check if any required setting is missing
    if (serverAddress == null || serverAddress.isEmpty) {
      debugPrint('Server address is missing');
      settingsComplete = false;
    }

    if (registrationCode == null || registrationCode.isEmpty) {
      debugPrint('Registration code is missing');
      settingsComplete = false;
    }

    // if (groupName == null || groupName.isEmpty) {
    //   debugPrint('Group name is missing');
    //   settingsComplete = false;
    // }

    // if (deviceAlias == null || deviceAlias.isEmpty) {
    //   debugPrint('Device alias is missing');
    //   settingsComplete = false;
    // }

    if (!settingsComplete && mounted) {
      // Navigate to settings page if any required setting is missing
      debugPrint(
        'Navigating to settings page due to missing required settings',
      );
      await Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const SettingsScreen()),
      );

      // After returning from settings, check again
      _checkRequiredSettings();
      return;
    }

    // If all settings are complete, register device and check for existing files
    if (settingsComplete) {
      debugPrint(
        'All required settings are complete, registering device with server',
      );

      // Register device with the server
      await _registerDeviceWithServer();

      debugPrint('Checking for existing files');
      final hasExistingFile = await fileProvider.findAndUseExistingFile();

      if (hasExistingFile && mounted && fileProvider.indexHtmlFile != null) {
        // If existing file found, open it in WebView
        debugPrint('Opening existing file in WebView');

        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                WebViewScreen(htmlFile: fileProvider.indexHtmlFile!),
          ),
        );
      }

      // Initialize MQTT provider
      mqttProvider.initialize();

      // Listen for MQTT messages
      mqttProvider.listenForMessages(_handleMqttMessage);

      // Set a timer to start accepting MQTT messages after a delay
      // This prevents processing retained messages immediately on startup
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          setState(() {
            _ignoreInitialMessages = false;
          });
          debugPrint('Now accepting MQTT messages after startup delay');
        }
      });
    }
  }

  // Handle MQTT message
  void _handleMqttMessage(MqttMessageModel message) async {
    // Ignore messages during initial startup period to avoid processing retained messages
    if (_ignoreInitialMessages) {
      debugPrint('Ignoring MQTT message during startup period');
      return;
    }

    // Check if message has file list
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in MQTT message');
      return;
    }

    final fileProvider = Provider.of<FileProvider>(context, listen: false);
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final deviceAlias = settingsProvider.settings.deviceAlias ?? '';

    // Process message based on type
    switch (message.type) {
      case 1: // Partial send
        await _processPartialMessage(message, deviceAlias, fileProvider);
        break;
      case 2: // All send
        await _processAllMessage(message, fileProvider);
        break;
      case 3: // Rule-based send
        await _processRuleBasedMessage(message, deviceAlias, fileProvider);
        break;
      default:
        debugPrint('Unknown message type: ${message.type}');
    }
  }

  // Process partial message (type 1)
  Future<void> _processPartialMessage(
    MqttMessageModel message,
    String deviceAlias,
    FileProvider fileProvider,
  ) async {
    // Get group name from settings
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final groupName = settingsProvider.settings.groupName ?? '';
    // 添加空值检查
    final msgGroupName = message.groupName ?? '';
    // Check group name match
    if (msgGroupName != '*' && !msgGroupName.contains(groupName)) {
      debugPrint(
        'Group name does not match: ${message.groupName} != $groupName',
      );
      return;
    }

    // Check if file list exists and is not empty
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in message');
      return;
    }

    // Process each file in the list
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('Skipping file item with empty download URL');
        continue;
      }

      if (fileItem.equipmentAliasName == '*' ||
          fileItem.equipmentAliasName == deviceAlias) {
        await _downloadAndProcessFile(fileItem.downloadFile!, fileProvider);
      }
    }
  }

  // Process all message (type 2)
  Future<void> _processAllMessage(
    MqttMessageModel message,
    FileProvider fileProvider,
  ) async {
    // Check if file list exists and is not empty
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in message');
      return;
    }

    // Process all files in the list
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('Skipping file item with empty download URL');
        continue;
      }

      await _downloadAndProcessFile(fileItem.downloadFile!, fileProvider);
    }
  }

  // Process rule-based message (type 3)
  Future<void> _processRuleBasedMessage(
    MqttMessageModel message,
    String deviceAlias,
    FileProvider fileProvider,
  ) async {
    // Get group name from settings
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final groupName = settingsProvider.settings.groupName ?? '';
    // 添加空值检查
    final msgGroupName = message.groupName ?? '';
    // Check group name match
    if (msgGroupName != '*' && !msgGroupName.contains(groupName)) {
      debugPrint(
        'Group name does not match: ${message.groupName} != $groupName',
      );
      return;
    }

    // Check if file list exists and is not empty
    if (message.fileList == null || message.fileList!.isEmpty) {
      debugPrint('No files to process in message');
      return;
    }

    // Process each file in the list based on equipment alias
    for (final fileItem in message.fileList!) {
      if (fileItem.downloadFile == null || fileItem.downloadFile!.isEmpty) {
        debugPrint('Skipping file item with empty download URL');
        continue;
      }

      if (fileItem.equipmentAliasName == '*' ||
          fileItem.equipmentAliasName == deviceAlias) {
        await _downloadAndProcessFile(fileItem.downloadFile!, fileProvider);
      }
    }
  }

  // Download and process file
  Future<void> _downloadAndProcessFile(
    String fileUrl,
    FileProvider fileProvider,
  ) async {
    if (fileUrl.isEmpty) {
      debugPrint('Empty file URL');
      return;
    }

    // Get server address from settings
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final serverAddress = settingsProvider.settings.mqttServerAddress ?? '';
    final serverPort = settingsProvider.settings.serverPort ?? '';
    // Check if URL starts with http or https
    String fullUrl = fileUrl;
    if (!fileUrl.toLowerCase().startsWith('http')) {
      // URL doesn't start with http, add server address as prefix
      if (serverAddress.isEmpty) {
        debugPrint('Server address is empty, cannot prepend to URL: $fileUrl');
        return;
      }

      // Ensure server address has http:// prefix
      String prefix = serverAddress.toLowerCase().startsWith('http')
          ? serverAddress
          : 'http://$serverAddress:$serverPort';

      // Ensure path starts with / if needed
      if (!fileUrl.startsWith('/') && !prefix.endsWith('/')) {
        fullUrl = '$prefix/$fileUrl';
      } else {
        fullUrl = '$prefix$fileUrl';
      }

      debugPrint('Modified URL: $fullUrl (original: $fileUrl)');
    } else {
      debugPrint('Using original URL: $fileUrl');
    }

    // Process file from URL
    final success = await fileProvider.processFileFromUrl(fullUrl);

    if (success && mounted) {
      // Check if this is a document file or ZIP file
      if (FileUtils.isDocumentFile(fullUrl)) {
        // Check if this is a PDF file - navigate to PDF viewer
        if (FileUtils.isPdfFile(fullUrl) &&
            fileProvider.downloadedFile != null) {
          debugPrint('Navigating to PDF viewer');
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  PdfViewerScreen(pdfFile: fileProvider.downloadedFile!),
            ),
          );
        } else if (FileUtils.isOfficeFile(fullUrl) &&
            fileProvider.downloadedFile != null) {
          debugPrint('Navigating to Office viewer');
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) => OfficeViewerScreen(
                filePath: fileProvider.downloadedFile!.path,
              ),
            ),
          );
        } else {
          // Non-PDF document files are opened directly by the system
          debugPrint('Document file opened successfully');
        }
      } else if (FileUtils.isZipFile(fullUrl) &&
          fileProvider.indexHtmlFile != null) {
        // Navigate to WebView screen for ZIP files

        //判断file_type 是html类型还是pdf类型

        print("Navigate to WebView screen for ZIP files");
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) =>
                WebViewScreen(htmlFile: fileProvider.indexHtmlFile!),
          ),
        );
      }
    }
  }

  // Navigate to settings screen
  void _navigateToSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(context.l10n.appTitle),
        actions: [
          // MQTT Status Indicator
          Consumer<MqttProvider>(
            builder: (context, mqttProvider, child) {
              return Padding(
                padding: const EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Text(
                      mqttProvider.isConnected
                          ? context.l10n.connected
                          : context.l10n.disconnected,
                      style: TextStyle(
                        color: mqttProvider.isConnected
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    const SizedBox(width: 4),
                    Icon(
                      mqttProvider.isConnected
                          ? Icons.cloud_done
                          : Icons.cloud_off,
                      color: mqttProvider.isConnected
                          ? Colors.green
                          : Colors.red,
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
      body: GestureDetector(
        onTap: _recordUserInteraction,
        onScaleStart: (_) => _recordUserInteraction(),
        child: MultiTouchGestureDetector(
          onDoubleTapWithTwoFingers: () {
            _recordUserInteraction();
            _navigateToSettings();
          },
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  context.l10n.appTitle,
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  context.l10n.doubleTapWithTwoFingers,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),

                // MQTT Status
                Consumer<MqttProvider>(
                  builder: (context, mqttProvider, child) {
                    return Column(
                      children: [
                        Text(
                          context.l10n.mqttStatus(
                            mqttProvider.connectionState
                                .toString()
                                .split('.')
                                .last,
                          ),
                          style: TextStyle(
                            color: mqttProvider.isConnected
                                ? Colors.green
                                : Colors.red,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (!mqttProvider.isConnected)
                          ElevatedButton(
                            onPressed: mqttProvider.connect,
                            child: Text(context.l10n.connect),
                          ),
                        if (mqttProvider.error.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              mqttProvider.error,
                              style: const TextStyle(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    );
                  },
                ),

                const SizedBox(height: 32),

                // File Operation Status
                Consumer<FileProvider>(
                  builder: (context, fileProvider, child) {
                    if (fileProvider.state == FileOperationState.idle) {
                      return const SizedBox.shrink();
                    }

                    return Column(
                      children: [
                        Text(
                          context.l10n.fileOperation(
                            fileProvider.state.toString().split('.').last,
                          ),
                          style: TextStyle(
                            color:
                                fileProvider.state == FileOperationState.error
                                ? Colors.red
                                : Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 8),
                        if (fileProvider.state == FileOperationState.checking)
                          const CircularProgressIndicator(),
                        if (fileProvider.state ==
                            FileOperationState.downloading)
                          Column(
                            children: [
                              const SizedBox(height: 8),
                              // Show download progress
                              LinearProgressIndicator(
                                value: fileProvider.downloadProgress,
                                backgroundColor: Colors.grey[300],
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  Colors.blue,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                '${(fileProvider.downloadProgress * 100).toStringAsFixed(1)}%',
                                style: const TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        if (fileProvider.state == FileOperationState.extracting)
                          const CircularProgressIndicator(),
                        if (fileProvider.state ==
                            FileOperationState.openingDocument)
                          Column(
                            children: [
                              const SizedBox(height: 8),
                              const CircularProgressIndicator(),
                              const SizedBox(height: 8),
                              Text(
                                context.l10n.openingDocument,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.blue,
                                ),
                              ),
                            ],
                          ),
                        if (fileProvider.state ==
                            FileOperationState.usingExisting)
                          Column(
                            children: [
                              const SizedBox(height: 8),
                              Text(
                                context.l10n.usingExistingFile,
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.green,
                                ),
                              ),
                              const SizedBox(height: 4),
                              LinearProgressIndicator(
                                value: 1.0, // 100% progress
                                backgroundColor: Colors.grey[300],
                                valueColor: const AlwaysStoppedAnimation<Color>(
                                  Colors.green,
                                ),
                              ),
                            ],
                          ),
                        if (fileProvider.error.isNotEmpty)
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Text(
                              fileProvider.error,
                              style: const TextStyle(color: Colors.red),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
