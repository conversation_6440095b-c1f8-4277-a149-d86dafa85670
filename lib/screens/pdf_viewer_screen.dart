import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:provider/provider.dart';
import '../providers/settings_provider.dart';
import '../services/orientation_service.dart';
import '../services/report_service.dart';

class PdfViewerScreen extends StatefulWidget {
  final File pdfFile;

  const PdfViewerScreen({super.key, required this.pdfFile});

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  PDFViewController? _controller;
  int _currentPage = 1;
  int _totalPages = 0;
  DateTime? _loadStartTime;

  final ReportService _reportService = ReportService();

  @override
  void initState() {
    super.initState();

    _loadStartTime = DateTime.now();

    // Apply orientation setting
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _applyOrientationSetting();
    });
  }

  // Apply orientation setting from settings
  Future<void> _applyOrientationSetting() async {
    final settingsProvider = Provider.of<SettingsProvider>(
      context,
      listen: false,
    );
    final orientation =
        settingsProvider.settings.screenOrientation ?? 'landscape';
    await OrientationService.applyOrientation(orientation);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: true,
      onPopInvokedWithResult: (didPop, result) {
        if (didPop) {
          // Reset to fullscreen when going back
          SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
        }
      },
      child: Scaffold(
        backgroundColor: Colors.black,
        body: Stack(
          children: [
            // PDF Viewer
            PDFView(
              filePath: widget.pdfFile.path,
              enableSwipe: true,
              swipeHorizontal: false,
              autoSpacing: false,
              pageFling: true,
              pageSnap: true,
              defaultPage: 0,
              fitPolicy: FitPolicy.BOTH,
              preventLinkNavigation: false,
              backgroundColor: Colors.black,
              onRender: (pages) {
                setState(() {
                  _totalPages = pages ?? 0;
                });

                // 上报PDF预览完成
                if (_loadStartTime != null) {
                  final loadDuration =
                      DateTime.now()
                          .difference(_loadStartTime!)
                          .inMilliseconds /
                      1000.0;
                  _reportService.reportDocumentPreviewCompleted(
                    filePath: widget.pdfFile.path,
                    fileType: 'pdf',
                    fileSize: widget.pdfFile.lengthSync(),
                    previewMethod: 'in_app',
                    loadDuration: loadDuration,
                  );
                }
              },
              onError: (error) {
                debugPrint('PDF Error: $error');

                // 上报PDF预览失败
                _reportService.reportDocumentPreviewFailed(
                  filePath: widget.pdfFile.path,
                  fileType: 'pdf',
                  fileSize: widget.pdfFile.lengthSync(),
                  previewMethod: 'in_app',
                  errorMessage: 'PDF Error: $error',
                );
              },
              onPageError: (page, error) {
                debugPrint('PDF Page Error: $page - $error');

                // 上报PDF页面错误
                _reportService.reportSystemError(
                  module: 'pdf_viewer',
                  message: 'PDF Page Error: $page - $error',
                  additionalInfo: {
                    'filePath': widget.pdfFile.path,
                    'page': page,
                  },
                );
              },
              onViewCreated: (PDFViewController pdfViewController) {
                _controller = pdfViewController;
              },
              onLinkHandler: (String? uri) {
                debugPrint('PDF Link: $uri');
              },
              onPageChanged: (int? page, int? total) {
                setState(() {
                  _currentPage =
                      (page ?? 0) + 1; // PDFView uses 0-based indexing
                  _totalPages = total ?? 0;
                });
              },
            ),
            // Page indicator overlay
            if (_totalPages > 0)
              Positioned(
                top: MediaQuery.of(context).padding.top + 16,
                right: 16,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$_currentPage / $_totalPages',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            // Back button overlay
            Positioned(
              top: MediaQuery.of(context).padding.top + 16,
              left: 16,
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: IconButton(
                  icon: const Icon(Icons.arrow_back, color: Colors.white),
                  onPressed: () => Navigator.of(context).pop(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
