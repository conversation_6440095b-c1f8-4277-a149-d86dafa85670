import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:mqtt_client/mqtt_client.dart';
import 'package:mqtt_client/mqtt_server_client.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/mqtt_message_model.dart';
import 'settings_service.dart';
import 'broadcast_service.dart';
import 'report_service.dart';
import 'error_handler_service.dart';

enum MqttConnectionState {
  disconnected,
  connecting,
  connected,
  disconnecting,
  error,
}

class MqttService {
  MqttServerClient? _client;
  final SettingsService _settingsService = SettingsService();
  final BroadcastService _broadcastService = BroadcastService();
  final ReportService _reportService = ReportService();
  final ErrorHandlerService _errorHandler = ErrorHandlerService();

  // Stream controllers
  final StreamController<MqttConnectionState> _connectionStateController =
      StreamController<MqttConnectionState>.broadcast();
  final StreamController<MqttMessageModel> _messageController =
      StreamController<MqttMessageModel>.broadcast();

  // Streams
  Stream<MqttConnectionState> get connectionState =>
      _connectionStateController.stream;
  Stream<MqttMessageModel> get messages => _messageController.stream;

  // Current connection state
  MqttConnectionState _currentState = MqttConnectionState.disconnected;
  MqttConnectionState get currentState => _currentState;
  Timer? _reconnectTimer; // Timer for automatic reconnection
  int _reconnectAttempts = 0; // Track number of reconnection attempts
  static const int _maxReconnectAttempts =
      10; // Maximum number of reconnection attempts

  // Check network connectivity
  Future<bool> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult.contains(ConnectivityResult.none)) {
        print('No network connectivity');
        return false;
      }
      return true;
    } catch (e) {
      print('Error checking connectivity: $e');
      return true; // Assume connectivity if we can't check
    }
  }

  // Check DNS resolution for a hostname
  Future<bool> _checkDnsResolution(String host) async {
    try {
      print('Checking DNS resolution for: $host');
      final List<InternetAddress> addresses = await InternetAddress.lookup(
        host,
      );
      if (addresses.isNotEmpty) {
        print('DNS resolution successful: ${addresses.first.address}');
        return true;
      }
      print('DNS resolution failed: No addresses found');
      return false;
    } on SocketException catch (e) {
      print('DNS resolution failed: $e');
      return false;
    } catch (e) {
      print('Error checking DNS resolution: $e');
      return false;
    }
  }

  // 检查MQTT客户端的实际连接状态
  bool _isActuallyConnected() {
    return _client != null &&
        _client!.connectionStatus != null &&
        _client!.connectionStatus!.state.toString() ==
            'MqttConnectionState.connected';
  }

  // Connect to MQTT server
  Future<bool> connect() async {
    // Check if already connected
    if (_currentState == MqttConnectionState.connected &&
        _isActuallyConnected()) {
      return true;
    }

    // 如果状态不一致，先断开现有连接
    if (_client != null && !_isActuallyConnected()) {
      print('Detected inconsistent connection state, cleaning up...');
      await disconnect();
    }

    // Check network connectivity
    final hasConnectivity = await _checkConnectivity();
    if (!hasConnectivity) {
      print('No network connectivity detected');
      _updateConnectionState(MqttConnectionState.error);
      return false;
    }

    try {
      // Load settings
      var settings = await _settingsService.loadSettings();
      if (settings?.mqttServerAddress == null ||
          settings!.mqttServerAddress!.isEmpty) {
        print(
          'MQTT server address is not set, starting broadcast discovery...',
        );
        final discoveryResult = await _broadcastService.listenForBroadcast();

        if (discoveryResult != null) {
          final serverAddress = discoveryResult['serverAddress'];
          if (serverAddress != null) {
            print('Discovered server address: $serverAddress');
            await _settingsService.updateMqttServerAddress(serverAddress);
            // Reload settings after update
            settings = await _settingsService.loadSettings();
          }
        } else {
          print('Could not discover server address via broadcast.');
          _updateConnectionState(MqttConnectionState.error);
          return false;
        }
      }

      // Use server address directly without protocol
      final server = settings!.mqttServerAddress!;
      print('Connecting to MQTT server: $server'); // Debug log

      // Check DNS resolution
      final hasDnsResolution = await _checkDnsResolution(server);
      if (!hasDnsResolution) {
        print('DNS resolution failed for: $server');
        _updateConnectionState(MqttConnectionState.error);
        return false;
      }

      // Use port from settings or default to 1883
      final port = int.tryParse(settings.mqttPort ?? '1883') ?? 1883;
      print('Using MQTT port: $port'); // Debug log

      // Create client
      _client = MqttServerClient(server, settings.deviceId ?? 'esop_client');
      _client!.port = port;
      _client!.keepAlivePeriod = 30; // 减少到30秒，提高心跳频率
      _client!.onDisconnected = _onDisconnected;
      _client!.onConnected = _onConnected;
      _client!.onSubscribed = _onSubscribed;

      // 添加自动重连和ping响应处理
      _client!.autoReconnect = false; // 禁用自动重连，使用我们自己的重连逻辑
      _client!.onAutoReconnect = _onAutoReconnect;
      _client!.onAutoReconnected = _onAutoReconnected;
      _client!.pongCallback = _onPong;

      // Set logging
      _client!.logging(on: false); // 关闭详细日志以减少噪音

      // Set connection message timeout
      _client!.connectTimeoutPeriod = 10000; // 增加到10秒

      // Set connection message
      final connMess = MqttConnectMessage()
          .withClientIdentifier(settings.deviceId ?? 'esop_client')
          .withWillTopic('willtopic')
          .withWillMessage('Will message')
          .startClean()
          .withWillQos(MqttQos.atLeastOnce);
      _client!.connectionMessage = connMess;

      // Update connection state
      _updateConnectionState(MqttConnectionState.connecting);

      // Connect to server
      await _client!.connect();

      // Subscribe to topic (use configured topic or default to esopChannel)
      final topic = settings.mqttTopic ?? 'esopChannel';
      _client!.subscribe(topic, MqttQos.atLeastOnce);

      // Listen for messages
      _client!.updates!.listen(_onMessage);

      return true;
    } catch (e) {
      print('Error connecting to MQTT server: $e');
      _updateConnectionState(MqttConnectionState.error);

      // 上报MQTT连接错误
      _errorHandler.reportMqttError(
        operation: 'connect',
        errorMessage: 'Error connecting to MQTT server: $e',
        additionalInfo: {'error': e.toString()},
      );

      // Explicitly trigger disconnection to start reconnection
      _onDisconnected();
      return false;
    }
  }

  // Disconnect from MQTT server
  Future<void> disconnect() async {
    try {
      _updateConnectionState(MqttConnectionState.disconnecting);
      _client?.disconnect();
    } catch (e) {
      print('Error disconnecting from MQTT server: $e');
    }
  }

  // Handle disconnection
  void _onDisconnected() {
    _updateConnectionState(MqttConnectionState.disconnected);
    _client = null;

    // 停止连接状态监控
    _stopConnectionMonitoring();

    // Start reconnection timer if not already running and under max attempts
    if ((_reconnectTimer == null || !_reconnectTimer!.isActive) &&
        _reconnectAttempts < _maxReconnectAttempts) {
      // Calculate delay with exponential backoff (10s, 20s, 40s... max 5min)
      final delaySeconds = min(10 * pow(2, _reconnectAttempts).toInt(), 300);
      _reconnectAttempts++;

      print(
        'MQTT disconnected, attempt $_reconnectAttempts/$_maxReconnectAttempts, '
        'scheduling reconnection in ${delaySeconds}s',
      );

      _reconnectTimer = Timer(Duration(seconds: delaySeconds), () async {
        if (_currentState == MqttConnectionState.disconnected) {
          print(
            'Attempting MQTT reconnection (attempt $_reconnectAttempts)...',
          );
          await connect();
        }
      });
    } else if (_reconnectAttempts >= _maxReconnectAttempts) {
      print(
        'Warning: Maximum reconnection attempts ($_maxReconnectAttempts) reached. '
        'Automatic reconnection disabled.',
      );
    }
  }

  // Handle connection
  void _onConnected() {
    _updateConnectionState(MqttConnectionState.connected);

    // Cancel any pending reconnection timer
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      print('MQTT connected, canceling reconnection timer');
      _reconnectTimer!.cancel();
      _reconnectTimer = null;
    }

    // Reset reconnection attempts counter
    if (_reconnectAttempts > 0) {
      print('MQTT connection restored after $_reconnectAttempts attempts');
      _reconnectAttempts = 0;
    }

    // 启动连接状态监控
    _startConnectionMonitoring();
  }

  Timer? _connectionMonitorTimer;

  // 启动连接状态监控
  void _startConnectionMonitoring() {
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = Timer.periodic(Duration(seconds: 15), (timer) {
      if (_client != null && _currentState == MqttConnectionState.connected) {
        // 检查实际连接状态
        if (!_isActuallyConnected()) {
          print(
            'Connection monitor detected disconnection, triggering reconnect...',
          );
          _onDisconnected();
        } else {
          // 检查连接状态，如果keep-alive超时会自动触发断开
          // MQTT客户端库会自动处理ping/pong，我们只需要监控状态
          print('MQTT connection monitor: connection appears healthy');
        }
      }
    });
  }

  // 停止连接状态监控
  void _stopConnectionMonitoring() {
    _connectionMonitorTimer?.cancel();
    _connectionMonitorTimer = null;
  }

  // Handle subscription
  void _onSubscribed(String topic) {
    print('Subscribed to topic: $topic');
  }

  // Handle message
  void _onMessage(List<MqttReceivedMessage<MqttMessage>> messages) {
    for (var message in messages) {
      final recMess = message.payload as MqttPublishMessage;
      final payload = MqttPublishPayload.bytesToStringAsString(
        recMess.payload.message,
      );

      print('Received MQTT message: $payload');

      try {
        final jsonData = jsonDecode(payload);

        // 上报MQTT指令接收
        _reportMqttCommandReceived(message.topic, payload, jsonData);

        // Validate message format
        if (jsonData['type'] == null) {
          print('Invalid MQTT message: missing required type field');
          _reportMqttCommandProcessed(
            message.topic,
            payload,
            jsonData,
            'failed',
            'Missing required type field',
          );
          continue;
        }

        // Validate type value (1, 2 or 3)
        final type = jsonData['type'];
        if (type != 1 && type != 2 && type != 3) {
          print('Invalid MQTT message type: $type (must be 1, 2 or 3)');
          _reportMqttCommandProcessed(
            message.topic,
            payload,
            jsonData,
            'failed',
            'Invalid message type: $type (must be 1, 2 or 3)',
          );
          continue;
        }

        if (jsonData['group_name'] == null) {
          print('Invalid MQTT message: missing required group_name field');
          _reportMqttCommandProcessed(
            message.topic,
            payload,
            jsonData,
            'failed',
            'Missing required group_name field',
          );
          continue;
        }

        if (jsonData['list'] == null || jsonData['list'] is! List) {
          print('Invalid MQTT message: missing or invalid list field');
          _reportMqttCommandProcessed(
            message.topic,
            payload,
            jsonData,
            'failed',
            'Missing or invalid list field',
          );
          continue;
        }

        // Validate each item in the list
        bool hasValidationError = false;
        String validationErrorMessage = '';

        for (var item in jsonData['list']) {
          if (item['download_file'] == null) {
            print('Invalid MQTT message item: missing download_file field');
            hasValidationError = true;
            validationErrorMessage = 'Missing download_file field in list item';
            break;
          }
          if (item['equipment_alias_name'] == null) {
            print(
              'Invalid MQTT message item: missing equipment_alias_name field',
            );
            hasValidationError = true;
            validationErrorMessage =
                'Missing equipment_alias_name field in list item';
            break;
          }
        }

        if (hasValidationError) {
          _reportMqttCommandProcessed(
            message.topic,
            payload,
            jsonData,
            'failed',
            validationErrorMessage,
          );
          continue;
        }

        final mqttMessage = MqttMessageModel.fromJson(jsonData);

        // 上报MQTT指令处理成功
        _reportMqttCommandProcessed(
          message.topic,
          payload,
          jsonData,
          'success',
          null,
        );

        _messageController.add(mqttMessage);
      } catch (e) {
        print('Error parsing MQTT message: $e');
        // 上报解析错误
        _reportMqttCommandProcessed(
          message.topic,
          payload,
          {},
          'failed',
          'Error parsing MQTT message: $e',
        );
      }
    }
  }

  /// 上报MQTT指令接收
  void _reportMqttCommandReceived(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
  ) {
    try {
      final messageType = jsonData['type'] ?? 0;
      final messageGroupName = jsonData['group_name'] ?? '';
      final fileList = jsonData['list'] as List? ?? [];

      _reportService.reportMqttCommandReceived(
        mqttTopic: topic,
        messageType: messageType,
        messageGroupName: messageGroupName,
        fileCount: fileList.length,
        commandContent: payload,
        processingStatus: 'received',
      );
    } catch (e) {
      print('Error reporting MQTT command received: $e');
    }
  }

  /// 上报MQTT指令处理结果
  void _reportMqttCommandProcessed(
    String topic,
    String payload,
    Map<String, dynamic> jsonData,
    String status,
    String? errorMessage,
  ) {
    try {
      final messageType = jsonData['type'] ?? 0;
      final messageGroupName = jsonData['group_name'] ?? '';
      final fileList = jsonData['list'] as List? ?? [];

      _reportService.reportMqttCommandProcessed(
        mqttTopic: topic,
        messageType: messageType,
        messageGroupName: messageGroupName,
        fileCount: fileList.length,
        commandContent: payload,
        processingStatus: status,
        errorMessage: errorMessage,
      );
    } catch (e) {
      print('Error reporting MQTT command processed: $e');
    }
  }

  // Handle auto reconnect
  void _onAutoReconnect() {
    print('MQTT auto reconnect triggered');
    _updateConnectionState(MqttConnectionState.connecting);
  }

  // Handle auto reconnected
  void _onAutoReconnected() {
    print('MQTT auto reconnected successfully');
    _updateConnectionState(MqttConnectionState.connected);
    _reconnectAttempts = 0; // Reset reconnection attempts
  }

  // Handle pong response
  void _onPong() {
    print('MQTT pong received - connection is alive');
  }

  // Update connection state
  void _updateConnectionState(MqttConnectionState state) {
    _currentState = state;
    _connectionStateController.add(state);
  }

  // Dispose resources
  void dispose() {
    // Cancel reconnection timer if active
    if (_reconnectTimer != null && _reconnectTimer!.isActive) {
      print('Disposing MQTT service, canceling reconnection timer');
      _reconnectTimer!.cancel();
      _reconnectTimer = null;
    }

    // 停止连接状态监控
    _stopConnectionMonitoring();

    _connectionStateController.close();
    _messageController.close();
    _broadcastService.close();
    disconnect();
  }
}
