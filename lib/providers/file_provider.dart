import 'dart:io';
import 'package:flutter/foundation.dart';
import '../services/file_service.dart';
import '../services/report_service.dart';
import '../utils/file_utils.dart';

enum FileOperationState {
  idle,
  checking,
  downloading,
  extracting,
  completed,
  error,
  usingExisting,
  openingDocument, // New state for opening document files
}

class FileProvider with ChangeNotifier {
  final FileService _fileService = FileService();
  final ReportService _reportService = ReportService();

  FileOperationState _state = FileOperationState.idle;
  String _error = '';
  File? _downloadedFile;
  String? _extractedDirPath;
  File? _indexHtmlFile;
  double _downloadProgress = 0.0; // Track download progress

  // Getters
  FileOperationState get state => _state;
  String get error => _error;
  File? get downloadedFile => _downloadedFile;
  String? get extractedDirPath => _extractedDirPath;
  File? get indexHtmlFile => _indexHtmlFile;
  double get downloadProgress =>
      _downloadProgress; // Getter for download progress

  // Update download progress
  void _updateDownloadProgress(double progress) {
    _downloadProgress = progress;
    notifyListeners();
  }

  // Process file from URL
  Future<bool> processFileFromUrl(
    String url, {
    bool forceDownload = false,
  }) async {
    // Reset state
    _state = FileOperationState.checking;
    _error = '';
    _downloadedFile = null;
    _extractedDirPath = null;
    _indexHtmlFile = null;
    _downloadProgress = 0.0;
    notifyListeners();

    try {
      // Check if this is a document file or ZIP file
      if (FileUtils.isDocumentFile(url)) {
        return await _processDocumentFile(url, forceDownload);
      } else if (FileUtils.isZipFile(url)) {
        return await _processZipFile(url, forceDownload);
      } else {
        _error = 'Unsupported file type: ${FileUtils.getFileExtension(url)}';
        _state = FileOperationState.error;
        notifyListeners();
        return false;
      }
    } catch (e) {
      _error = 'Error processing file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  // Process document file (PDF, PPT, Excel, etc.)
  Future<bool> _processDocumentFile(String url, bool forceDownload) async {
    final startTime = DateTime.now();
    final fileName = url.split('/').last;
    final fileType = FileUtils.getFileExtension(url);

    try {
      // Check if document file already exists
      if (!forceDownload) {
        final existingFile = await _fileService.checkExistingDocumentFile(url);
        if (existingFile != null) {
          debugPrint('Using existing document file: ${existingFile.path}');
          _downloadedFile = existingFile;
          _downloadProgress = 1.0; // Set progress to 100%
          _state = FileOperationState.usingExisting;
          notifyListeners();

          // Short delay to show the "Using existing file" state
          await Future.delayed(const Duration(milliseconds: 500));

          // Check if this is a PDF file - handle differently
          if (FileUtils.isPdfFile(existingFile.path)) {
            debugPrint('PDF file detected, ready for in-app viewing');

            // 上报文档预览开始
            await _reportService.reportDocumentPreviewStarted(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'in_app',
            );

            _state = FileOperationState.completed;
            notifyListeners();

            // 上报文档预览完成
            final duration =
                DateTime.now().difference(startTime).inMilliseconds / 1000.0;
            await _reportService.reportDocumentPreviewCompleted(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'in_app',
              loadDuration: duration,
            );

            return true;
          }

          // For non-PDF files, open with system application
          _state = FileOperationState.openingDocument;
          notifyListeners();

          // 上报文档预览开始
          await _reportService.reportDocumentPreviewStarted(
            filePath: existingFile.path,
            fileType: fileType,
            fileSize: await existingFile.length(),
            previewMethod: 'system_app',
          );

          final opened = await _fileService.openDocumentFile(existingFile);
          if (opened) {
            _state = FileOperationState.completed;
            notifyListeners();

            // 上报文档预览完成
            final duration =
                DateTime.now().difference(startTime).inMilliseconds / 1000.0;
            await _reportService.reportDocumentPreviewCompleted(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'system_app',
              loadDuration: duration,
            );

            return true;
          } else {
            _error = 'Failed to open document file';
            _state = FileOperationState.error;
            notifyListeners();

            // 上报文档预览失败
            await _reportService.reportDocumentPreviewFailed(
              filePath: existingFile.path,
              fileType: fileType,
              fileSize: await existingFile.length(),
              previewMethod: 'system_app',
              errorMessage: _error,
            );

            return false;
          }
        }
      }

      // 上报文件下载开始
      await _reportService.reportFileDownloadStarted(
        fileUrl: url,
        fileName: fileName,
        fileType: fileType,
      );

      // Download the document file
      _state = FileOperationState.downloading;
      notifyListeners();

      final downloadStartTime = DateTime.now();
      final file = await _fileService.downloadFile(
        url,
        onProgress: _updateDownloadProgress,
        forceDownload: forceDownload,
      );

      if (file == null) {
        _error = 'Failed to download document file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文件下载失败
        final duration =
            DateTime.now().difference(downloadStartTime).inMilliseconds /
            1000.0;
        await _reportService.reportFileDownloadFailed(
          fileUrl: url,
          fileName: fileName,
          fileType: fileType,
          operationDuration: duration,
          errorMessage: _error,
        );

        return false;
      }

      _downloadedFile = file;

      // 上报文件下载完成
      final downloadDuration =
          DateTime.now().difference(downloadStartTime).inMilliseconds / 1000.0;
      await _reportService.reportFileDownloadCompleted(
        fileUrl: url,
        fileName: fileName,
        fileSize: await file.length(),
        fileType: fileType,
        operationDuration: downloadDuration,
        localPath: file.path,
      );

      // Check if this is a PDF file - handle differently
      if (FileUtils.isPdfFile(file.path)) {
        debugPrint('PDF file downloaded, ready for in-app viewing');

        // 上报文档预览开始
        await _reportService.reportDocumentPreviewStarted(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'in_app',
        );

        _state = FileOperationState.completed;
        notifyListeners();

        // 上报文档预览完成
        final duration =
            DateTime.now().difference(startTime).inMilliseconds / 1000.0;
        await _reportService.reportDocumentPreviewCompleted(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'in_app',
          loadDuration: duration,
        );

        return true;
      }

      // For non-PDF files, open with system application
      _state = FileOperationState.openingDocument;
      notifyListeners();

      // 上报文档预览开始
      await _reportService.reportDocumentPreviewStarted(
        filePath: file.path,
        fileType: fileType,
        fileSize: await file.length(),
        previewMethod: 'system_app',
      );

      final opened = await _fileService.openDocumentFile(file);
      if (opened) {
        _state = FileOperationState.completed;
        notifyListeners();

        // 上报文档预览完成
        final duration =
            DateTime.now().difference(startTime).inMilliseconds / 1000.0;
        await _reportService.reportDocumentPreviewCompleted(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'system_app',
          loadDuration: duration,
        );

        return true;
      } else {
        _error = 'Failed to open document file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文档预览失败
        await _reportService.reportDocumentPreviewFailed(
          filePath: file.path,
          fileType: fileType,
          fileSize: await file.length(),
          previewMethod: 'system_app',
          errorMessage: _error,
        );

        return false;
      }
    } catch (e) {
      _error = 'Error processing document file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();

      // 上报系统错误
      await _reportService.reportSystemError(
        module: 'file',
        message: 'Error processing document file: $e',
        additionalInfo: {
          'url': url,
          'fileName': fileName,
          'fileType': fileType,
        },
      );

      return false;
    }
  }

  // Process ZIP file (existing logic)
  Future<bool> _processZipFile(String url, bool forceDownload) async {
    final fileName = url.split('/').last;
    final fileType = FileUtils.getFileExtension(url);

    try {
      // Check if file already exists and has been extracted
      if (!forceDownload) {
        final existingFile = await _fileService.checkExistingFile(url);
        if (existingFile != null) {
          debugPrint('Using existing file: ${existingFile['file'].path}');
          _downloadedFile = existingFile['file'];
          _extractedDirPath = existingFile['extractedDirPath'];
          _indexHtmlFile = existingFile['indexHtmlFile'];
          _downloadProgress = 1.0; // Set progress to 100%
          _state = FileOperationState.usingExisting;
          notifyListeners();

          // Short delay to show the "Using existing file" state
          await Future.delayed(const Duration(milliseconds: 500));

          _state = FileOperationState.completed;
          notifyListeners();
          return true;
        }
      }

      // 上报文件下载开始
      await _reportService.reportFileDownloadStarted(
        fileUrl: url,
        fileName: fileName,
        fileType: fileType,
      );

      // If we get here, we need to download the file
      _state = FileOperationState.downloading;
      notifyListeners();

      final downloadStartTime = DateTime.now();
      // Download file with progress reporting
      final file = await _fileService.downloadFile(
        url,
        onProgress: _updateDownloadProgress,
        forceDownload: forceDownload,
      );

      if (file == null) {
        _error = 'Failed to download file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文件下载失败
        final duration =
            DateTime.now().difference(downloadStartTime).inMilliseconds /
            1000.0;
        await _reportService.reportFileDownloadFailed(
          fileUrl: url,
          fileName: fileName,
          fileType: fileType,
          operationDuration: duration,
          errorMessage: _error,
        );

        return false;
      }

      _downloadedFile = file;

      // 上报文件下载完成
      final downloadDuration =
          DateTime.now().difference(downloadStartTime).inMilliseconds / 1000.0;
      await _reportService.reportFileDownloadCompleted(
        fileUrl: url,
        fileName: fileName,
        fileSize: await file.length(),
        fileType: fileType,
        operationDuration: downloadDuration,
        localPath: file.path,
      );

      // 上报文件解压开始
      await _reportService.reportFileExtractionStarted(
        filePath: file.path,
        fileName: fileName,
        fileType: fileType,
      );

      _state = FileOperationState.extracting;
      notifyListeners();

      final extractStartTime = DateTime.now();
      // Extract ZIP file
      final extractPath = await _fileService.extractZipFile(file);

      if (extractPath == null) {
        _error = 'Failed to extract ZIP file';
        _state = FileOperationState.error;
        notifyListeners();

        // 上报文件解压失败
        final duration =
            DateTime.now().difference(extractStartTime).inMilliseconds / 1000.0;
        await _reportService.reportFileExtractionFailed(
          filePath: file.path,
          fileName: fileName,
          fileType: fileType,
          operationDuration: duration,
          errorMessage: _error,
        );

        return false;
      }

      _extractedDirPath = extractPath;

      // 上报文件解压完成
      final extractDuration =
          DateTime.now().difference(extractStartTime).inMilliseconds / 1000.0;
      await _reportService.reportFileExtractionCompleted(
        filePath: file.path,
        fileName: fileName,
        fileSize: await file.length(),
        fileType: fileType,
        operationDuration: extractDuration,
        extractPath: extractPath,
      );

      // Find index.html file
      final indexFile = await _fileService.findIndexHtmlFile(extractPath);

      if (indexFile == null) {
        _error = 'Failed to find index.html file';
        _state = FileOperationState.error;
        notifyListeners();
        return false;
      }

      _indexHtmlFile = indexFile;
      _state = FileOperationState.completed;
      notifyListeners();

      return true;
    } catch (e) {
      _error = 'Error processing ZIP file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  // Clean up temporary files
  Future<void> cleanup() async {
    try {
      await _fileService.cleanupTempFiles();
    } catch (e) {
      debugPrint('Error cleaning up temporary files: $e');
    }
  }

  // Find and use existing HTML file
  Future<bool> findAndUseExistingFile() async {
    _state = FileOperationState.checking;
    _error = '';
    _downloadedFile = null;
    _extractedDirPath = null;
    _indexHtmlFile = null;
    _downloadProgress = 0.0;
    notifyListeners();

    try {
      final indexFile = await _fileService.findExistingHtmlFile();

      if (indexFile != null) {
        _indexHtmlFile = indexFile;
        _downloadProgress = 1.0; // Set progress to 100%
        _state = FileOperationState.usingExisting;
        notifyListeners();

        // Short delay to show the "Using existing file" state
        await Future.delayed(const Duration(milliseconds: 500));

        _state = FileOperationState.completed;
        notifyListeners();
        return true;
      }

      // No existing file found
      _state = FileOperationState.idle;
      notifyListeners();
      return false;
    } catch (e) {
      _error = 'Error finding existing file: $e';
      _state = FileOperationState.error;
      debugPrint(_error);
      notifyListeners();
      return false;
    }
  }

  // Reset state
  void reset() {
    _state = FileOperationState.idle;
    _error = '';
    _downloadedFile = null;
    _extractedDirPath = null;
    _indexHtmlFile = null;
    _downloadProgress = 0.0;
    notifyListeners();
  }
}
