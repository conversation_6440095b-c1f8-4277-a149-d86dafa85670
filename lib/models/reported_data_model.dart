/// 上报数据模型
/// 用于向 equipment/reported 接口上报设备状态和操作信息
class ReportedDataModel {
  /// 设备MAC地址
  final String macAddress;

  /// 注册码
  final String registrationCode;

  /// 设备别名
  final String deviceAlias;

  /// 组名
  final String groupName;

  /// 上报时间戳 (ISO 8601格式)
  final String timestamp;

  /// 上报类型
  final ReportType reportType;

  /// 操作唯一标识
  final String operationId;

  /// 具体数据内容
  final Map<String, dynamic> data;

  ReportedDataModel({
    required this.macAddress,
    required this.registrationCode,
    required this.deviceAlias,
    required this.groupName,
    required this.timestamp,
    required this.reportType,
    required this.operationId,
    required this.data,
  });

  /// 从JSON创建实例
  factory ReportedDataModel.fromJson(Map<String, dynamic> json) {
    return ReportedDataModel(
      macAddress: json['mac_address'] ?? '',
      registrationCode: json['registration_code'] ?? '',
      deviceAlias: json['device_alias'] ?? '',
      groupName: json['group_name'] ?? '',
      timestamp: json['timestamp'] ?? '',
      reportType: ReportType.fromString(json['report_type'] ?? ''),
      operationId: json['operation_id'] ?? '',
      data: json['data'] ?? {},
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'mac_address': macAddress,
      'registration_code': registrationCode,
      'device_alias': deviceAlias,
      'group_name': groupName,
      'timestamp': timestamp,
      'report_type': reportType.value,
      'operation_id': operationId,
      'data': data,
    };
  }

  /// 创建MQTT指令接收上报
  factory ReportedDataModel.mqttCommandReceived({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String mqttTopic,
    required int messageType,
    required String messageGroupName,
    required int fileCount,
    required String commandContent,
    required String processingStatus,
    String? errorMessage,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.mqttCommandReceived,
      operationId: operationId,
      data: {
        'mqtt_topic': mqttTopic,
        'message_type': messageType,
        'message_group_name': messageGroupName,
        'file_count': fileCount,
        'command_content': commandContent,
        'processing_status': processingStatus,
        if (errorMessage != null) 'error_message': errorMessage,
      },
    );
  }

  /// 创建MQTT指令处理完成上报
  factory ReportedDataModel.mqttCommandProcessed({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String mqttTopic,
    required int messageType,
    required String messageGroupName,
    required int fileCount,
    required String commandContent,
    required String processingStatus,
    String? errorMessage,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.mqttCommandProcessed,
      operationId: operationId,
      data: {
        'mqtt_topic': mqttTopic,
        'message_type': messageType,
        'message_group_name': messageGroupName,
        'file_count': fileCount,
        'command_content': commandContent,
        'processing_status': processingStatus,
        if (errorMessage != null) 'error_message': errorMessage,
      },
    );
  }

  /// 创建文件下载开始上报
  factory ReportedDataModel.fileDownloadStarted({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String fileUrl,
    required String fileName,
    required String fileType,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.fileDownloadStarted,
      operationId: operationId,
      data: {
        'file_url': fileUrl,
        'file_name': fileName,
        'file_type': fileType,
        'download_progress': 0.0,
        'status': 'started',
      },
    );
  }

  /// 创建文件下载完成上报
  factory ReportedDataModel.fileDownloadCompleted({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String fileUrl,
    required String fileName,
    required int fileSize,
    required String fileType,
    required double operationDuration,
    required String localPath,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.fileDownloadCompleted,
      operationId: operationId,
      data: {
        'file_url': fileUrl,
        'file_name': fileName,
        'file_size': fileSize,
        'file_type': fileType,
        'download_progress': 1.0,
        'operation_duration': operationDuration,
        'status': 'success',
        'local_path': localPath,
      },
    );
  }

  /// 创建文件下载失败上报
  factory ReportedDataModel.fileDownloadFailed({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String fileUrl,
    required String fileName,
    required String fileType,
    required double operationDuration,
    required String errorMessage,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.fileDownloadFailed,
      operationId: operationId,
      data: {
        'file_url': fileUrl,
        'file_name': fileName,
        'file_type': fileType,
        'operation_duration': operationDuration,
        'status': 'failed',
        'error_message': errorMessage,
      },
    );
  }

  /// 创建文件解压开始上报
  factory ReportedDataModel.fileExtractionStarted({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String filePath,
    required String fileName,
    required String fileType,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.fileExtractionStarted,
      operationId: operationId,
      data: {
        'file_path': filePath,
        'file_name': fileName,
        'file_type': fileType,
        'status': 'started',
      },
    );
  }

  /// 创建文件解压完成上报
  factory ReportedDataModel.fileExtractionCompleted({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String filePath,
    required String fileName,
    required int fileSize,
    required String fileType,
    required double operationDuration,
    required String extractPath,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.fileExtractionCompleted,
      operationId: operationId,
      data: {
        'file_path': filePath,
        'file_name': fileName,
        'file_size': fileSize,
        'file_type': fileType,
        'operation_duration': operationDuration,
        'status': 'success',
        'extract_path': extractPath,
      },
    );
  }

  /// 创建文件解压失败上报
  factory ReportedDataModel.fileExtractionFailed({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String filePath,
    required String fileName,
    required String fileType,
    required double operationDuration,
    required String errorMessage,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.fileExtractionFailed,
      operationId: operationId,
      data: {
        'file_path': filePath,
        'file_name': fileName,
        'file_type': fileType,
        'operation_duration': operationDuration,
        'status': 'failed',
        'error_message': errorMessage,
      },
    );
  }

  /// 创建文档预览开始上报
  factory ReportedDataModel.documentPreviewStarted({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String filePath,
    required String fileType,
    required int fileSize,
    required String previewMethod,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.documentPreviewStarted,
      operationId: operationId,
      data: {
        'file_path': filePath,
        'file_type': fileType,
        'file_size': fileSize,
        'preview_method': previewMethod,
        'status': 'started',
      },
    );
  }

  /// 创建文档预览完成上报
  factory ReportedDataModel.documentPreviewCompleted({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String filePath,
    required String fileType,
    required int fileSize,
    required String previewMethod,
    required double loadDuration,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.documentPreviewCompleted,
      operationId: operationId,
      data: {
        'file_path': filePath,
        'file_type': fileType,
        'file_size': fileSize,
        'preview_method': previewMethod,
        'load_duration': loadDuration,
        'status': 'success',
      },
    );
  }

  /// 创建文档预览失败上报
  factory ReportedDataModel.documentPreviewFailed({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String filePath,
    required String fileType,
    required int fileSize,
    required String previewMethod,
    required String errorMessage,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.documentPreviewFailed,
      operationId: operationId,
      data: {
        'file_path': filePath,
        'file_type': fileType,
        'file_size': fileSize,
        'preview_method': previewMethod,
        'status': 'failed',
        'error_message': errorMessage,
      },
    );
  }

  /// 创建WebView加载完成上报
  factory ReportedDataModel.webviewLoaded({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String url,
    required String htmlFilePath,
    required double loadDuration,
    String? userAgent,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.webviewLoaded,
      operationId: operationId,
      data: {
        'url': url,
        'html_file_path': htmlFilePath,
        'load_duration': loadDuration,
        'status': 'success',
        if (userAgent != null) 'user_agent': userAgent,
      },
    );
  }

  /// 创建WebView加载错误上报
  factory ReportedDataModel.webviewError({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String url,
    required String htmlFilePath,
    required String errorMessage,
    String? userAgent,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.webviewError,
      operationId: operationId,
      data: {
        'url': url,
        'html_file_path': htmlFilePath,
        'status': 'failed',
        'error_message': errorMessage,
        if (userAgent != null) 'user_agent': userAgent,
      },
    );
  }

  /// 创建系统错误上报
  factory ReportedDataModel.systemError({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String module,
    required String message,
    String? errorCode,
    String? stackTrace,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.systemError,
      operationId: operationId,
      data: {
        'log_level': 'error',
        'module': module,
        'message': message,
        if (errorCode != null) 'error_code': errorCode,
        if (stackTrace != null) 'stack_trace': stackTrace,
        if (additionalInfo != null) 'additional_info': additionalInfo,
      },
    );
  }

  /// 创建应用日志上报
  factory ReportedDataModel.applicationLog({
    required String macAddress,
    required String registrationCode,
    required String deviceAlias,
    required String groupName,
    required String operationId,
    required String logLevel,
    required String module,
    required String message,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ReportedDataModel(
      macAddress: macAddress,
      registrationCode: registrationCode,
      deviceAlias: deviceAlias,
      groupName: groupName,
      timestamp: DateTime.now().toUtc().toIso8601String(),
      reportType: ReportType.applicationLog,
      operationId: operationId,
      data: {
        'log_level': logLevel,
        'module': module,
        'message': message,
        if (additionalInfo != null) 'additional_info': additionalInfo,
      },
    );
  }

  /// 生成操作ID
  static String generateOperationId(String prefix) {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final random = (timestamp % 1000).toString().padLeft(3, '0');
    return '${prefix}_${timestamp}_$random';
  }
}

/// 上报类型枚举
enum ReportType {
  /// MQTT指令接收
  mqttCommandReceived('mqtt_command_received'),

  /// MQTT指令处理完成
  mqttCommandProcessed('mqtt_command_processed'),

  /// 文件下载开始
  fileDownloadStarted('file_download_started'),

  /// 文件下载完成
  fileDownloadCompleted('file_download_completed'),

  /// 文件下载失败
  fileDownloadFailed('file_download_failed'),

  /// 文件解压开始
  fileExtractionStarted('file_extraction_started'),

  /// 文件解压完成
  fileExtractionCompleted('file_extraction_completed'),

  /// 文件解压失败
  fileExtractionFailed('file_extraction_failed'),

  /// 文档预览开始
  documentPreviewStarted('document_preview_started'),

  /// 文档预览完成
  documentPreviewCompleted('document_preview_completed'),

  /// 文档预览失败
  documentPreviewFailed('document_preview_failed'),

  /// WebView加载完成
  webviewLoaded('webview_loaded'),

  /// WebView加载错误
  webviewError('webview_error'),

  /// 系统错误
  systemError('system_error'),

  /// 应用日志
  applicationLog('application_log');

  const ReportType(this.value);

  final String value;

  /// 从字符串创建枚举值
  static ReportType fromString(String value) {
    for (ReportType type in ReportType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return ReportType.applicationLog; // 默认值
  }
}
