# Equipment Reported API 接口规范

## 概述

本文档定义了 `equipment/reported` 接口的数据格式规范，用于终端设备向服务器上报状态和操作信息。

## 接口信息

- **接口路径**: `/v1/equipment/reported`
- **请求方法**: `POST`
- **内容类型**: `application/json`

## 数据结构

### 基础数据结构

所有上报数据都包含以下基础字段：

```json
{
  "mac_address": "设备MAC地址",
  "registration_code": "注册码",
  "device_alias": "设备别名",
  "group_name": "组名",
  "timestamp": "上报时间戳(ISO 8601格式)",
  "report_type": "上报类型",
  "operation_id": "操作唯一标识",
  "data": {
    // 根据report_type不同，包含不同的数据结构
  }
}
```

### 上报类型 (report_type)

| 类型 | 值 | 描述 |
|------|-----|------|
| MQTT指令接收 | `mqtt_command_received` | 接收到MQTT指令 |
| MQTT指令处理完成 | `mqtt_command_processed` | MQTT指令处理完成 |
| 文件下载开始 | `file_download_started` | 文件下载开始 |
| 文件下载完成 | `file_download_completed` | 文件下载完成 |
| 文件下载失败 | `file_download_failed` | 文件下载失败 |
| 文件解压开始 | `file_extraction_started` | 文件解压开始 |
| 文件解压完成 | `file_extraction_completed` | 文件解压完成 |
| 文件解压失败 | `file_extraction_failed` | 文件解压失败 |
| 文档预览开始 | `document_preview_started` | 文档预览开始 |
| 文档预览完成 | `document_preview_completed` | 文档预览完成 |
| 文档预览失败 | `document_preview_failed` | 文档预览失败 |
| WebView加载完成 | `webview_loaded` | WebView加载完成 |
| WebView加载错误 | `webview_error` | WebView加载错误 |
| 系统错误 | `system_error` | 系统错误 |
| 应用日志 | `application_log` | 应用日志 |

## 具体数据格式示例

### 1. MQTT指令接收上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:30:45.123Z",
  "report_type": "mqtt_command_received",
  "operation_id": "mqtt_cmd_recv_1737282645123_456",
  "data": {
    "mqtt_topic": "esopChannel",
    "message_type": 1,
    "message_group_name": "Production-Line-A",
    "file_count": 2,
    "command_content": "{\"type\":1,\"group_name\":\"Production-Line-A\",\"list\":[...]}",
    "processing_status": "received"
  }
}
```

### 2. 文件下载完成上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:32:15.456Z",
  "report_type": "file_download_completed",
  "operation_id": "file_dl_comp_1737282735456_789",
  "data": {
    "file_url": "http://server.com/files/manual.zip",
    "file_name": "manual.zip",
    "file_size": 2048000,
    "file_type": "zip",
    "download_progress": 1.0,
    "operation_duration": 8.5,
    "status": "success",
    "local_path": "/storage/emulated/0/Android/data/com.example.esop_client/files/zippak/manual.zip"
  }
}
```

### 3. 文档预览完成上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:35:22.789Z",
  "report_type": "document_preview_completed",
  "operation_id": "doc_prev_comp_1737282922789_012",
  "data": {
    "file_path": "/storage/emulated/0/Android/data/com.example.esop_client/files/documents/manual.pdf",
    "file_type": "pdf",
    "file_size": 1024000,
    "preview_method": "in_app",
    "load_duration": 2.1,
    "status": "success"
  }
}
```

### 4. WebView加载完成上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:38:45.123Z",
  "report_type": "webview_loaded",
  "operation_id": "webview_load_1737283125123_345",
  "data": {
    "url": "file:///storage/emulated/0/Android/data/com.example.esop_client/files/extracted/index.html",
    "html_file_path": "/storage/emulated/0/Android/data/com.example.esop_client/files/extracted/index.html",
    "load_duration": 3.5,
    "status": "success",
    "user_agent": "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36"
  }
}
```

### 5. 系统错误上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:40:15.678Z",
  "report_type": "system_error",
  "operation_id": "sys_err_1737283215678_901",
  "data": {
    "log_level": "error",
    "module": "mqtt",
    "message": "MQTT connection lost",
    "error_code": "MQTT_CONN_LOST",
    "additional_info": {
      "server_address": "*************",
      "port": 1883,
      "reconnect_attempts": 3
    }
  }
}
```

### 6. 应用日志上报

```json
{
  "mac_address": "AA:BB:CC:DD:EE:FF",
  "registration_code": "REG123456",
  "device_alias": "Terminal-001",
  "group_name": "Production-Line-A",
  "timestamp": "2025-01-19T10:42:30.234Z",
  "report_type": "application_log",
  "operation_id": "app_log_1737283350234_567",
  "data": {
    "log_level": "info",
    "module": "user_action",
    "message": "User action: double_tap on main_screen",
    "additional_info": {
      "action": "double_tap",
      "screen": "main_screen",
      "timestamp": "2025-01-19T10:42:30.234Z"
    }
  }
}
```

## 批量上报接口

### 接口信息

- **接口路径**: `/v1/equipment/reported/batch`
- **请求方法**: `POST`
- **内容类型**: `application/json`

### 请求格式

```json
{
  "reports": [
    {
      // 单个上报数据，格式同上
    },
    {
      // 另一个上报数据
    }
    // ... 更多上报数据
  ]
}
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "received_count": 1,
    "processed_count": 1
  }
}
```

### 错误响应

```json
{
  "code": 1,
  "message": "error message",
  "data": null
}
```

## 注意事项

1. **时间戳格式**: 所有时间戳都使用 ISO 8601 格式的 UTC 时间
2. **操作ID**: 每个操作都有唯一的操作ID，格式为 `{prefix}_{timestamp}_{random}`
3. **文件大小**: 以字节为单位
4. **持续时间**: 以秒为单位，支持小数
5. **状态值**: 通常为 `success`、`failed`、`started` 等
6. **批量上报**: 建议每次批量上报不超过50条记录
7. **重试机制**: 客户端应实现重试机制，失败的上报数据会重新加入队列

## 数据字段说明

### 公共字段

- `mac_address`: 设备的MAC地址，用于唯一标识设备
- `registration_code`: 设备注册码
- `device_alias`: 设备别名，用户可配置
- `group_name`: 设备所属组名
- `timestamp`: 上报时间戳
- `report_type`: 上报类型，见上表
- `operation_id`: 操作唯一标识，用于追踪和去重
- `data`: 具体的上报数据，根据类型不同而不同

### data字段详细说明

根据不同的 `report_type`，`data` 字段包含不同的内容。具体字段说明请参考上述示例。
