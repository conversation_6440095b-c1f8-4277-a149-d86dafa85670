# 自动恢复功能说明

## 功能概述

当程序满足以下所有条件时，用户停留在首页不操作10秒后，系统将自动恢复打开之前下发的文件：

### 触发条件

1. **注册码通过** - 注册码已配置且有效
2. **MQTT连接成功** - MQTT客户端已连接到服务器
3. **网络连通** - 设备网络连接正常
4. **已有下发文件** - 系统中存在之前下发的文件

### 用户交互检测

- 系统会检测用户的各种交互行为：
  - 点击屏幕
  - 滑动手势
  - 缩放手势
  - 双指双击手势

- 如果在10秒倒计时期间检测到任何用户交互，自动恢复将被取消

## 实现逻辑

### 1. 初始化检查

```dart
void _startAutoRecoveryCheck() {
  // 延迟5秒后开始检查，确保所有初始化完成
  Timer(const Duration(seconds: 5), () {
    _checkAutoRecoveryConditions();
  });
}
```

### 2. 条件检查

系统会检查以下条件：

#### 注册码验证
```dart
Future<bool> _isRegistrationCodeValid(SettingsProvider settingsProvider) async {
  final settings = settingsProvider.settings;
  final registrationCode = settings.registrationCode;
  final serverAddress = settings.mqttServerAddress;
  
  // 基本检查：注册码和服务器地址不能为空
  if (registrationCode == null || registrationCode.isEmpty) {
    return false;
  }
  
  if (serverAddress == null || serverAddress.isEmpty) {
    return false;
  }
  
  return true;
}
```

#### MQTT连接状态
```dart
final isMqttConnected = mqttProvider.isConnected;
```

#### 网络连通性
```dart
Future<bool> _checkNetworkConnectivity() async {
  try {
    final connectivityResult = await Connectivity().checkConnectivity();
    return !connectivityResult.contains(ConnectivityResult.none);
  } catch (e) {
    return false;
  }
}
```

#### 已下发文件检查
```dart
Future<bool> _hasDownloadedFiles(FileProvider fileProvider) async {
  try {
    final hasExistingFile = await fileProvider.findAndUseExistingFile();
    return hasExistingFile;
  } catch (e) {
    return false;
  }
}
```

### 3. 倒计时机制

当所有条件满足时，启动10秒倒计时：

```dart
void _startAutoRecoveryCountdown(FileProvider fileProvider) {
  // 记录开始倒计时的时间
  _lastUserInteraction = DateTime.now();
  
  _autoRecoveryTimer = Timer(const Duration(seconds: 10), () async {
    // 检查在倒计时期间是否有用户交互
    final now = DateTime.now();
    final timeSinceLastInteraction = now.difference(_lastUserInteraction!).inSeconds;
    
    if (timeSinceLastInteraction >= 10) {
      await _performAutoRecovery(fileProvider);
    } else {
      debugPrint('User interaction detected, canceling auto recovery');
    }
  });
}
```

### 4. 用户交互检测

```dart
void _recordUserInteraction() {
  _lastUserInteraction = DateTime.now();
  debugPrint('User interaction recorded');
}
```

UI层面的交互检测：
```dart
body: GestureDetector(
  onTap: _recordUserInteraction,
  onPanStart: (_) => _recordUserInteraction(),
  onScaleStart: (_) => _recordUserInteraction(),
  child: MultiTouchGestureDetector(
    onDoubleTapWithTwoFingers: () {
      _recordUserInteraction();
      _navigateToSettings();
    },
    // ... 其他UI组件
  ),
),
```

### 5. 自动恢复执行

```dart
Future<void> _performAutoRecovery(FileProvider fileProvider) async {
  try {
    final hasExistingFile = await fileProvider.findAndUseExistingFile();
    
    if (hasExistingFile && mounted) {
      if (fileProvider.indexHtmlFile != null) {
        // 打开HTML文件
        Navigator.push(context, MaterialPageRoute(
          builder: (context) => WebViewScreen(htmlFile: fileProvider.indexHtmlFile!),
        ));
      } else if (fileProvider.downloadedFile != null) {
        // 打开文档文件
        final filePath = fileProvider.downloadedFile!.path;
        if (FileUtils.isPdfFile(filePath)) {
          Navigator.push(context, MaterialPageRoute(
            builder: (context) => PdfViewerScreen(pdfFile: fileProvider.downloadedFile!),
          ));
        } else if (FileUtils.isOfficeFile(filePath)) {
          Navigator.push(context, MaterialPageRoute(
            builder: (context) => OfficeViewerScreen(filePath: filePath),
          ));
        }
      }
    }
  } catch (e) {
    debugPrint('Error performing auto recovery: $e');
  }
}
```

## 工作流程

1. **应用启动** → 延迟5秒开始检查
2. **条件检查** → 验证所有必要条件
3. **条件满足** → 启动10秒倒计时
4. **用户交互检测** → 监控用户操作
5. **倒计时完成** → 检查是否有用户交互
6. **自动恢复** → 打开已下发的文件

## 日志输出

系统会输出详细的调试日志：

```
Auto recovery check:
  Registration valid: true
  MQTT connected: true
  Network connected: true
  Has existing file: true
Starting auto recovery countdown (10 seconds)...
User interaction recorded
User interaction detected, canceling auto recovery
```

或者：

```
Auto recovery check:
  Registration valid: true
  MQTT connected: true
  Network connected: true
  Has existing file: true
Starting auto recovery countdown (10 seconds)...
Auto recovery triggered - opening existing file
Auto recovery: Opening HTML file
```

## 重试机制

如果条件不满足，系统会每5秒重新检查一次：

```dart
// 如果条件不满足，5秒后重新检查
Timer(const Duration(seconds: 5), () {
  _checkAutoRecoveryConditions();
});
```

## 资源管理

- 在页面销毁时自动取消定时器
- 避免内存泄漏
- 确保只在页面mounted状态下执行操作

## 使用场景

这个功能特别适用于以下场景：

1. **设备重启后** - 自动恢复到之前的工作状态
2. **网络恢复后** - 重新连接后自动打开文件
3. **应用重新启动** - 快速恢复到工作状态
4. **无人值守操作** - 减少人工干预需求

## 注意事项

1. **用户体验** - 10秒的等待时间平衡了自动化和用户控制
2. **条件严格** - 确保所有条件满足才触发，避免误操作
3. **交互优先** - 用户操作始终优先于自动恢复
4. **错误处理** - 完善的异常处理确保系统稳定性
