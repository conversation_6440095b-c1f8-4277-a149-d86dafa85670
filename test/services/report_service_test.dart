import 'package:flutter_test/flutter_test.dart';
import 'package:esop_client/models/reported_data_model.dart';
import 'package:esop_client/services/report_service.dart';

void main() {
  group('ReportService Tests', () {
    late ReportService reportService;

    setUp(() {
      reportService = ReportService();
    });

    tearDown(() {
      reportService.dispose();
    });

    test(
      'should create MQTT command received report with correct format',
      () async {
        // Arrange
        const macAddress = 'AA:BB:CC:DD:EE:FF';
        const registrationCode = 'REG123456';
        const deviceAlias = 'Terminal-001';
        const groupName = 'Production-Line-A';
        const operationId = 'mqtt_cmd_recv_test_001';
        const mqttTopic = 'esopChannel';
        const messageType = 1;
        const messageGroupName = 'Production-Line-A';
        const fileCount = 2;
        const commandContent =
            '{"type":1,"group_name":"Production-Line-A","list":[]}';
        const processingStatus = 'received';

        // Act
        final reportData = ReportedDataModel.mqttCommandReceived(
          macAddress: macAddress,
          registrationCode: registrationCode,
          deviceAlias: deviceAlias,
          groupName: groupName,
          operationId: operationId,
          mqttTopic: mqttTopic,
          messageType: messageType,
          messageGroupName: messageGroupName,
          fileCount: fileCount,
          commandContent: commandContent,
          processingStatus: processingStatus,
        );

        // Assert
        expect(reportData.macAddress, equals(macAddress));
        expect(reportData.registrationCode, equals(registrationCode));
        expect(reportData.deviceAlias, equals(deviceAlias));
        expect(reportData.groupName, equals(groupName));
        expect(reportData.operationId, equals(operationId));
        expect(reportData.reportType, equals(ReportType.mqttCommandReceived));
        expect(reportData.data['mqtt_topic'], equals(mqttTopic));
        expect(reportData.data['message_type'], equals(messageType));
        expect(reportData.data['message_group_name'], equals(messageGroupName));
        expect(reportData.data['file_count'], equals(fileCount));
        expect(reportData.data['command_content'], equals(commandContent));
        expect(reportData.data['processing_status'], equals(processingStatus));
      },
    );

    test(
      'should create file download completed report with correct format',
      () async {
        // Arrange
        const macAddress = 'AA:BB:CC:DD:EE:FF';
        const registrationCode = 'REG123456';
        const deviceAlias = 'Terminal-001';
        const groupName = 'Production-Line-A';
        const operationId = 'file_dl_comp_test_001';
        const fileUrl = 'http://server.com/files/manual.zip';
        const fileName = 'manual.zip';
        const fileSize = 2048000;
        const fileType = 'zip';
        const operationDuration = 8.5;
        const localPath =
            '/storage/emulated/0/Android/data/com.example.esop_client/files/zippak/manual.zip';

        // Act
        final reportData = ReportedDataModel.fileDownloadCompleted(
          macAddress: macAddress,
          registrationCode: registrationCode,
          deviceAlias: deviceAlias,
          groupName: groupName,
          operationId: operationId,
          fileUrl: fileUrl,
          fileName: fileName,
          fileSize: fileSize,
          fileType: fileType,
          operationDuration: operationDuration,
          localPath: localPath,
        );

        // Assert
        expect(reportData.macAddress, equals(macAddress));
        expect(reportData.registrationCode, equals(registrationCode));
        expect(reportData.deviceAlias, equals(deviceAlias));
        expect(reportData.groupName, equals(groupName));
        expect(reportData.operationId, equals(operationId));
        expect(reportData.reportType, equals(ReportType.fileDownloadCompleted));
        expect(reportData.data['file_url'], equals(fileUrl));
        expect(reportData.data['file_name'], equals(fileName));
        expect(reportData.data['file_size'], equals(fileSize));
        expect(reportData.data['file_type'], equals(fileType));
        expect(
          reportData.data['operation_duration'],
          equals(operationDuration),
        );
        expect(reportData.data['local_path'], equals(localPath));
        expect(reportData.data['status'], equals('success'));
        expect(reportData.data['download_progress'], equals(1.0));
      },
    );

    test('should create system error report with correct format', () async {
      // Arrange
      const macAddress = 'AA:BB:CC:DD:EE:FF';
      const registrationCode = 'REG123456';
      const deviceAlias = 'Terminal-001';
      const groupName = 'Production-Line-A';
      const operationId = 'sys_err_test_001';
      const module = 'mqtt';
      const message = 'MQTT connection lost';
      const errorCode = 'MQTT_CONN_LOST';
      const additionalInfo = {
        'server_address': '*************',
        'port': 1883,
        'reconnect_attempts': 3,
      };

      // Act
      final reportData = ReportedDataModel.systemError(
        macAddress: macAddress,
        registrationCode: registrationCode,
        deviceAlias: deviceAlias,
        groupName: groupName,
        operationId: operationId,
        module: module,
        message: message,
        errorCode: errorCode,
        additionalInfo: additionalInfo,
      );

      // Assert
      expect(reportData.macAddress, equals(macAddress));
      expect(reportData.registrationCode, equals(registrationCode));
      expect(reportData.deviceAlias, equals(deviceAlias));
      expect(reportData.groupName, equals(groupName));
      expect(reportData.operationId, equals(operationId));
      expect(reportData.reportType, equals(ReportType.systemError));
      expect(reportData.data['log_level'], equals('error'));
      expect(reportData.data['module'], equals(module));
      expect(reportData.data['message'], equals(message));
      expect(reportData.data['error_code'], equals(errorCode));
      expect(reportData.data['additional_info'], equals(additionalInfo));
    });

    test('should serialize to JSON correctly', () async {
      // Arrange
      final reportData = ReportedDataModel.mqttCommandReceived(
        macAddress: 'AA:BB:CC:DD:EE:FF',
        registrationCode: 'REG123456',
        deviceAlias: 'Terminal-001',
        groupName: 'Production-Line-A',
        operationId: 'test_001',
        mqttTopic: 'esopChannel',
        messageType: 1,
        messageGroupName: 'Production-Line-A',
        fileCount: 2,
        commandContent: '{"type":1}',
        processingStatus: 'received',
      );

      // Act
      final json = reportData.toJson();

      // Assert
      expect(json, isA<Map<String, dynamic>>());
      expect(json['mac_address'], equals('AA:BB:CC:DD:EE:FF'));
      expect(json['registration_code'], equals('REG123456'));
      expect(json['device_alias'], equals('Terminal-001'));
      expect(json['group_name'], equals('Production-Line-A'));
      expect(json['operation_id'], equals('test_001'));
      expect(json['report_type'], equals('mqtt_command_received'));
      expect(json['timestamp'], isA<String>());
      expect(json['data'], isA<Map<String, dynamic>>());
    });

    test('should generate unique operation IDs', () async {
      // Act
      final id1 = ReportedDataModel.generateOperationId('test');
      // Add a small delay to ensure different timestamps
      await Future.delayed(const Duration(milliseconds: 1));
      final id2 = ReportedDataModel.generateOperationId('test');

      // Assert
      expect(id1, isNot(equals(id2)));
      expect(id1, startsWith('test_'));
      expect(id2, startsWith('test_'));
    });

    test('should handle ReportType enum correctly', () {
      // Test all enum values
      expect(
        ReportType.mqttCommandReceived.value,
        equals('mqtt_command_received'),
      );
      expect(
        ReportType.fileDownloadCompleted.value,
        equals('file_download_completed'),
      );
      expect(ReportType.systemError.value, equals('system_error'));
      expect(ReportType.webviewLoaded.value, equals('webview_loaded'));

      // Test fromString method
      expect(
        ReportType.fromString('mqtt_command_received'),
        equals(ReportType.mqttCommandReceived),
      );
      expect(
        ReportType.fromString('file_download_completed'),
        equals(ReportType.fileDownloadCompleted),
      );
      expect(
        ReportType.fromString('unknown_type'),
        equals(ReportType.applicationLog),
      ); // Default value
    });
  });
}
